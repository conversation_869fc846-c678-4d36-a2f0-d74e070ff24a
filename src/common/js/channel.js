import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';

class Channel {
	messageHandlerName = 'MessageHandler';
	constructor() {
		this.channel = null;
		this.listeners = {};
		this.glbLoader = new GLTFLoader();

		if (!window.QWebChannel || !window.qt) {
			return;
		}
		new QWebChannel(qt.webChannelTransport, (channel) => {
			this.channel = channel;
			this.watch();
		});
	}

	watch() {
		if (!this.channel || !this.channel.objects.filehandler) {
			return;
		}

		const filehandler = this.channel.objects.filehandler;

		// 监听开始事件
		filehandler.start.connect(() => {
			if (this.listeners.onStart) {
				this.listeners.onStart();
			}
		});

		filehandler.statistics.connect((count, peopleNum) => {
			if (this.listeners.onStatistics) {
				this.listeners.onStatistics(count, peopleNum);
			}
		});

		// 监听数据块准备事件 (BAT文件)
		filehandler.bat_chunk_ready.connect((curtime, data) => {
			if (this.listeners.onDataBlockReady) {
				this.listeners.onDataBlockReady(curtime, data);
			}
		});

		// 监听GLB块准备事件
		filehandler.glb_chunk_ready.connect((chunk_index, total_chunk, data) => {
			if (this.listeners.onGlbChunkReady) {
				this.listeners.onGlbChunkReady(chunk_index, total_chunk, data);
			}
		});

		// 监听JSON块准备事件
		filehandler.json_chunk_ready.connect((chunk_index, total_chunk, data) => {
			if (this.listeners.onJsonChunkReady) {
				this.listeners.onJsonChunkReady(chunk_index, total_chunk, data);
			}
		});

		// 监听FDS块准备事件
		filehandler.fds_chunk_ready.connect((chunk_index, total_chunk, data) => {
			if (this.listeners.onFdsChunkReady) {
				this.listeners.onFdsChunkReady(chunk_index, total_chunk, data);
			}
		});

		// 监听结束事件
		filehandler.end.connect(() => {
			if (this.listeners.onEnd) {
				this.listeners.onEnd();
			}
		});
	}

	// 设置监听器
	setListeners(listeners) {
		this.listeners = { ...this.listeners, ...listeners };
	}

	// 清除监听器
	clearListeners() {
		this.listeners = {};
	}

	call(fn, data) {}

	async loadMoveData(processCallback) {
		if (!this.channel) {
			throw new Error('Channel未初始化');
		}

		return new Promise((resolve, reject) => {
			let json = [];

			let count = 0;
			let peopleNum = 0;
			let sum = 0;
			// 设置临时监听器
			this.setListeners({
				onStart: () => {
					json = [];
				},
				onStatistics: (_count, _peopleNum) => {
					count = _count;
					peopleNum = _peopleNum;
				},
				onDataBlockReady: (curtime, data) => {
					json.push({
						[curtime]: data,
					});
					sum += 1;
					processCallback(Math.floor((sum / count) * 100));
				},
				onEnd: () => {
					// 清除临时监听器
					this.clearListeners();
					resolve(json);
				},
			});

			this.channel.objects.filehandler.read_bat();
		});
	}

	async loadGLB(path, onProgress = null) {
		if (!this.channel) {
			throw new Error('Channel未初始化');
		}

		let binaryChunks = [];
		let totalChunks = 0;
		return new Promise((resolve, reject) => {
			// 设置临时监听器
			this.setListeners({
				onStart: () => {
					binaryChunks = [];
					totalChunks = 0;
					console.log('开始->GLB', path);
					// 调用进度回调，开始阶段
					if (onProgress) {
						onProgress(0, 0, 0); // (当前块, 总块数, 进度百分比)
					}
				},
				onGlbChunkReady: (chunk_index, total_chunk, data) => {
					// 将base64数据解码为二进制数据
					const binaryData = Uint8Array.from(atob(data), (c) =>
						c.charCodeAt(0)
					);
					binaryChunks[chunk_index] = binaryData;
					totalChunks = total_chunk;

					// 计算进度并调用回调
					const completedChunks = binaryChunks.filter((chunk) => chunk).length;
					const progress = Math.round((completedChunks / total_chunk) * 100);

					if (onProgress) {
						onProgress(completedChunks, total_chunk, progress);
					}
				},
				onEnd: () => {
					try {
						// 完成时调用进度回调
						if (onProgress) {
							onProgress(totalChunks, totalChunks, 100);
						}

						// 计算总长度
						const totalLength = binaryChunks.reduce(
							(sum, chunk) => sum + chunk.length,
							0
						);

						// 创建合并后的二进制数据
						const mergedBinary = new Uint8Array(totalLength);
						let offset = 0;
						for (const chunk of binaryChunks) {
							mergedBinary.set(chunk, offset);
							offset += chunk.length;
						}

						this.glbLoader.parse(mergedBinary.buffer, '', (gltf) => {
							resolve(gltf);
						});

						// 清除临时监听器
						this.clearListeners();
					} catch (error) {
						console.log('error->GLB', path);
						reject(error);
					}
				},
			});

			this.channel.objects.filehandler.read_glb(path);
		});
	}

	async loadJSON() {
		if (!this.channel) {
			throw new Error('Channel未初始化');
		}

		return new Promise((resolve, reject) => {
			let json = '';
			// 设置临时监听器
			this.setListeners({
				onStart: () => {
					console.log(`开始->JSON`);
					json = '';
				},
				onJsonChunkReady: (chunk_index, total_chunk, data) => {
					// JSON数据处理逻辑可在这里添加
					json += data;
				},
				onEnd: () => {
					console.log(`结束->JSON`);
					// 清除临时监听器
					this.clearListeners();
					resolve(JSON.parse(json));
				},
			});

			this.channel.objects.filehandler.read_json();
		});
	}

	async loadFBS(processCallback) {
		if (!this.channel) {
			throw new Error('Channel未初始化');
		}

		return new Promise((resolve, reject) => {
			let text = '';

			// 设置临时监听器
			this.setListeners({
				onStart: () => {
					text = '';
				},
				onFdsChunkReady: (chunk_index, total_chunk, data) => {
					text += data;

					processCallback(Math.floor((chunk_index / total_chunk) * 100));
				},
				onEnd: () => {
					// 清除临时监听器
					this.clearListeners();
					resolve(text);
				},
			});

			this.channel.objects.filehandler.read_fds();
		});
	}
}
window.Channel = new Channel();
